- **Property:** overflow
- **Shorthand:** ofl
  Utilities for controlling how content that overflows an element's bounds is handled.

```css
oflA {
  overflow: auto;
}
oflH {
  overflow: hidden;
}
oflV {
  overflow: visible;
}
oflS {
  overflow: scroll;
}
oflC {
  overflow: clip;
}
```

The `overflow` property controls what happens when content is too large to fit in an element's content area. It determines whether to clip content, show scrollbars, or let content overflow visibly.

## Overflow Auto

```css
oflA {
  overflow: auto;
}
```

Shows scrollbars only when content overflows. This is the most commonly used overflow value for creating scrollable containers.

**Example:**

```html
<div class="oflA h200px w300px p20px bgc#f5f5f5">
  <h3>Scrollable Content Area</h3>
  <p>
    This container has overflow: auto, which means scrollbars will appear
    automatically when the content exceeds the container's dimensions. This is
    perfect for creating scrollable text areas, content panels, and navigation
    menus.
  </p>
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod
    tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,
    quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
    consequat.
  </p>
  <p>
    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore
    eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident,
    sunt in culpa qui officia deserunt mollit anim id est laborum.
  </p>
</div>
```

## Overflow Hidden

```css
oflH {
  overflow: hidden;
}
```

Clips content that overflows the container. No scrollbars are shown, and overflowing content is completely hidden.

**Example:**

```html
<div class="oflH h200px w300px p20px bgc#f0f8ff">
  <h3>Hidden Overflow Content</h3>
  <p>
    This container uses overflow: hidden, which means any content that extends
    beyond the container's boundaries will be clipped and invisible. This is
    useful for creating clean layouts where you want to ensure content doesn't
    break out of its designated area.
  </p>
  <p>
    Notice how this text gets cut off when it reaches the bottom of the
    container. The hidden content is still part of the DOM but is visually
    clipped. This technique is often used for image containers, card components,
    and layout elements where precise boundaries are important.
  </p>
  <p>This paragraph will be completely hidden from view.</p>
</div>
```

## Overflow Visible

```css
oflV {
  overflow: visible;
}
```

Content overflows the container and remains visible. This is the default behavior for most elements.

**Example:**

```html
<div class="oflV h200px w300px p20px bgc#fff5ee bdc#ddd bd1px">
  <h3>Visible Overflow Content</h3>
  <p>
    This container has overflow: visible, which is the default behavior. Content
    that exceeds the container's dimensions will extend beyond the boundaries
    and remain visible.
  </p>
  <p>
    This can be useful when you want content to naturally flow outside its
    container, such as dropdown menus, tooltips, or decorative elements that
    should extend beyond their parent's boundaries.
  </p>
  <p>
    Notice how this text extends beyond the container's height limit but remains
    fully visible and readable. The container maintains its defined dimensions
    while allowing content to overflow naturally.
  </p>
</div>
```

## Overflow Scroll

```css
oflS {
  overflow: scroll;
}
```

Always shows scrollbars, regardless of whether content overflows. This ensures consistent layout even when content fits within the container.

**Example:**

```html
<div class="oflS h200px w300px p20px bgc#f0fff0">
  <h3>Always Scrollable Content</h3>
  <p>
    This container uses overflow: scroll, which means scrollbars are always
    visible, even when the content fits comfortably within the container
    dimensions.
  </p>
  <p>
    This approach ensures consistent spacing and layout, as the scrollbar space
    is always reserved. It's particularly useful in dynamic interfaces where
    content length may vary.
  </p>
</div>
```

## Overflow Clip

```css
oflC {
  overflow: clip;
}
```

Similar to hidden, but provides better performance by completely clipping content without creating a scrolling context.

**Example:**

```html
<div class="oflC h200px w300px p20px bgc#fdf5e6">
  <h3>Clipped Content Area</h3>
  <p>
    This container uses overflow: clip, which clips content similar to hidden
    but with better performance characteristics. The content is hard-clipped at
    the container boundaries.
  </p>
  <p>
    Unlike overflow: hidden, clip doesn't establish a new stacking context or
    affect the positioning of absolutely positioned descendants. This makes it
    ideal for performance-critical scenarios where simple content clipping is
    needed.
  </p>
  <p>This content will be clipped and not visible.</p>
</div>
```

## Directional Overflow Control

### Overflow X (Horizontal)

```css
oflxA {
  overflow-x: auto;
}
oflxH {
  overflow-x: hidden;
}
oflxV {
  overflow-x: visible;
}
oflxS {
  overflow-x: scroll;
}
oflxC {
  overflow-x: clip;
}
```

Controls horizontal overflow behavior independently from vertical overflow.

**Example:**

```html
<div class="oflxA oflyH h150px w300px p20px bgc#f8f8ff">
  <div class="w500px">
    <h3>Horizontal Scrolling Content</h3>
    <p>
      This content is wider than its container and will create a horizontal
      scrollbar while vertical overflow is hidden. This technique is useful for
      creating horizontal scrolling galleries, wide tables, or timeline
      components.
    </p>
  </div>
</div>
```

### Overflow Y (Vertical)

```css
oflyA {
  overflow-y: auto;
}
oflyH {
  overflow-y: hidden;
}
oflyV {
  overflow-y: visible;
}
oflyS {
  overflow-y: scroll;
}
oflyC {
  overflow-y: clip;
}
```

Controls vertical overflow behavior independently from horizontal overflow.

**Example:**

```html
<div class="oflxH oflyA h200px w300px p20px bgc#fffaf0">
  <h3>Vertical Scrolling Content</h3>
  <p>
    This container allows vertical scrolling while preventing horizontal
    overflow. This is the most common pattern for content areas, sidebars, and
    text containers.
  </p>
  <p>
    The vertical scrollbar appears when content exceeds the container height,
    while horizontal content is constrained to the container width. This creates
    a clean, predictable scrolling experience.
  </p>
  <p>
    Additional content continues to flow vertically, creating a natural reading
    experience with controlled boundaries. This pattern works well for articles,
    comments, and any text-heavy content areas.
  </p>
</div>
```

## Complete Overflow Behavior Grid

The following examples demonstrate all overflow utilities in a systematic grid layout:

```html
<!-- Row 1: Basic Overflow Values -->
<div class="oflA h150px w200px p15px bgc#f5f5f5">
  <h4>oflA (auto)</h4>
  <p>
    Scrollbars appear when needed. Content that exceeds container dimensions
    will be scrollable.
  </p>
  <p>Additional content to demonstrate overflow behavior.</p>
</div>

<div class="oflH h150px w200px p15px bgc#f0f8ff">
  <h4>oflH (hidden)</h4>
  <p>Content is clipped at container boundaries. No scrollbars are shown.</p>
  <p>This text will be hidden when it overflows.</p>
</div>

<div class="oflV h150px w200px p15px bgc#fff5ee">
  <h4>oflV (visible)</h4>
  <p>Content overflows and remains visible outside container bounds.</p>
  <p>This text extends beyond the container.</p>
</div>

<!-- Row 2: Scroll and Clip -->
<div class="oflS h150px w200px p15px bgc#f0fff0">
  <h4>oflS (scroll)</h4>
  <p>Scrollbars are always visible, regardless of content size.</p>
  <p>Consistent layout with reserved scrollbar space.</p>
</div>

<div class="oflC h150px w200px p15px bgc#fdf5e6">
  <h4>oflC (clip)</h4>
  <p>Content is hard-clipped with better performance than hidden.</p>
  <p>Optimized clipping without scrolling context.</p>
</div>

<!-- Row 3: Directional Control -->
<div class="oflxA oflyH h150px w200px p15px bgc#f8f8ff">
  <div class="w300px">
    <h4>oflxA oflyH</h4>
    <p>
      Horizontal scroll, vertical hidden. Wide content creates horizontal
      scrollbar.
    </p>
  </div>
</div>

<div class="oflxH oflyA h150px w200px p15px bgc#fffaf0">
  <h4>oflxH oflyA</h4>
  <p>
    Horizontal hidden, vertical scroll. Tall content creates vertical scrollbar
    only.
  </p>
  <p>Additional vertical content for demonstration.</p>
  <p>More content to show vertical scrolling.</p>
</div>

<div class="oflxS oflyS h150px w200px p15px bgc#f0f0f0">
  <div class="w300px">
    <h4>oflxS oflyS</h4>
    <p>
      Both scrollbars always visible. Consistent layout regardless of content
      size.
    </p>
    <p>Additional content for both directions.</p>
  </div>
</div>
```

This comprehensive grid demonstrates how each overflow utility affects content behavior, providing clear visual examples of when and how to use each option for optimal user experience and layout control.
