- **Property:** overflow
- **Shorthand:** ofl
  Utilities for controlling how content that overflows an element's bounds is handled.

```css
oflAuto {
  overflow: auto;
}
oflH {
  overflow: hidden;
}
oflV {
  overflow: visible;
}
oflScroll {
  overflow: scroll;
}
oflC {
  overflow: clip;
}
```

The `overflow` property controls what happens when content is too large to fit in an element's content area. It determines whether to clip content, show scrollbars, or let content overflow visibly.

## Overflow Auto

```css
oflAuto {
  overflow: auto;
}
```

Shows scrollbars only when content overflows. This is the most commonly used overflow value for creating scrollable containers.

**Example:**

```html
<div class="oflAuto">
  <p>
    This container has overflow: auto, which means scrollbars will appear
    automatically when the content exceeds the container's dimensions. This is
    perfect for creating scrollable text areas, content panels, and navigation
    menus.
  </p>
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod
    tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,
    quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo
    consequat.
  </p>
  <p>
    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore
    eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident,
    sunt in culpa qui officia deserunt mollit anim id est laborum.
  </p>
</div>
```

![Overflow auto example](./img/overflow/auto.png)

## Overflow Hidden

```css
oflH {
  overflow: hidden;
}
```

Clips content that overflows the container. No scrollbars are shown, and overflowing content is completely hidden.

**Example:**

```html
<div class="oflH">
  <!-- ...  -->
</div>
```

## Overflow Visible

```css
oflV {
  overflow: visible;
}
```

Content overflows the container and remains visible. This is the default behavior for most elements.

**Example:**

```html
<div class="oflV">
  <p>
    This container has overflow: visible, which is the default behavior. Content
    that exceeds the container's dimensions will extend beyond the boundaries
    and remain visible.
  </p>
  <p>
    This can be useful when you want content to naturally flow outside its
    container, such as dropdown menus, tooltips, or decorative elements that
    should extend beyond their parent's boundaries.
  </p>
  <p>
    Notice how this text extends beyond the container's height limit but remains
    fully visible and readable. The container maintains its defined dimensions
    while allowing content to overflow naturally.
  </p>
</div>
```

![Overflow visible example](./img/overflow/visible.png)

## Overflow Scroll

```css
oflScroll {
  overflow: scroll;
}
```

Always shows scrollbars, regardless of whether content overflows. This ensures consistent layout even when content fits within the container.

**Example:**

```html
<div class="oflScroll">
  <p>
    This container uses overflow: scroll, which means scrollbars are always
    visible, even when the content fits comfortably within the container
    dimensions.
  </p>
  <p>
    This approach ensures consistent spacing and layout, as the scrollbar space
    is always reserved. It's particularly useful in dynamic interfaces where
    content length may vary.
  </p>
</div>
```

![Overflow scroll example](./img/overflow/scroll.png)

## Directional Overflow Control

### Overflow X (Horizontal)

```css
oflxA {
  overflow-x: auto;
}
oflxH {
  overflow-x: hidden;
}
oflxV {
  overflow-x: visible;
}
oflxS {
  overflow-x: scroll;
}
oflxC {
  overflow-x: clip;
}
```

Controls horizontal overflow behavior independently from vertical overflow.

**Example:**

```html
<div class="oflxA oflyH">
  <p>
    This content is wider than its container and will create a horizontal
    scrollbar while vertical overflow is hidden. This technique is useful for
    creating horizontal scrolling galleries, wide tables, or timeline
    components.
  </p>
</div>
```

![Overflow horizontal scroll example](./img/overflow/x-scroll.png)

### Overflow Y (Vertical)

```css
oflyA {
  overflow-y: auto;
}
oflyH {
  overflow-y: hidden;
}
oflyV {
  overflow-y: visible;
}
oflyS {
  overflow-y: scroll;
}
oflyC {
  overflow-y: clip;
}
```

Controls vertical overflow behavior independently from horizontal overflow.

**Example:**

```html
<div class="oflxH oflyA">
  <p>
    This container allows vertical scrolling while preventing horizontal
    overflow. This is the most common pattern for content areas, sidebars, and
    text containers.
  </p>
  <p>
    The vertical scrollbar appears when content exceeds the container height,
    while horizontal content is constrained to the container width. This creates
    a clean, predictable scrolling experience.
  </p>
  <p>
    Additional content continues to flow vertically, creating a natural reading
    experience with controlled boundaries. This pattern works well for articles,
    comments, and any text-heavy content areas.
  </p>
</div>
```

![Overflow vertical scroll example](./img/overflow/y-scroll.png)
