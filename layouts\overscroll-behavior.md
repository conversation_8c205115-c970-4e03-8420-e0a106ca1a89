- **Property:** overscroll-behavior
- **Shorthand:** osrbh
  Utilities for controlling how content that overflows an element's bounds is handled.

```css
osrbhA {
  overscroll-behavior: auto;
}
osrbhC {
  overscroll-behavior: contain;
}
osrbhN {
  overscroll-behavior: none;
}
```

The `overscroll-behavior` property controls what happens when content is too large to fit in an element's content area. It determines whether to clip content, show scrollbars, or let content overflow visibly.
