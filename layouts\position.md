- **Property:** position
- **Shorthand:** pos
  Utilities for controlling the browser's behavior when reaching the boundary of a scrolling area.

```css
posS {
  position: static;
}
posF {
  position: fixed;
}
posA {
  position: absolute;
}
posR {
  position: relative;
}
posSt {
  position: sticky;
}
```

The `position` property controls what happens when a user reaches the scrolling boundary of an element. It determines whether to allow scroll chaining to parent elements, enable bounce effects, or disable overscroll behaviors entirely.
